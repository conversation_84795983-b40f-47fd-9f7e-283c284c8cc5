/**
 * 简化的Redis Stream和Pub/Sub演示服务器
 * 展示ClickHouse数据读取和Redis实时处理
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const Redis = require('ioredis');
const axios = require('axios');
const cors = require('cors');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../frontend')));

// Redis连接
const redis = new Redis({
  host: 'localhost',
  port: 6379,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3
});

const redisPub = new Redis({
  host: 'localhost',
  port: 6379
});

// ClickHouse配置
const CLICKHOUSE_CONFIG = {
  host: '***********',
  port: 8123,
  user: 'myuser',
  password: 'mypassword',
  database: 'mydatabase',
  table: 'force_orders'
};

// ClickHouse查询函数
async function queryClickHouse(sql) {
  try {
    const response = await axios.post(`http://${CLICKHOUSE_CONFIG.host}:${CLICKHOUSE_CONFIG.port}`, sql, {
      params: {
        user: CLICKHOUSE_CONFIG.user,
        password: CLICKHOUSE_CONFIG.password,
        database: CLICKHOUSE_CONFIG.database,
        default_format: 'JSON'
      },
      timeout: 30000
    });
    return response.data;
  } catch (error) {
    console.error('ClickHouse查询错误:', error.message);
    throw error;
  }
}

// 获取实时统计数据
async function getRealtimeStats() {
  try {
    const sql = `
      SELECT
        count() as total_orders,
        countIf(side = 'SELL') as sell_orders,
        countIf(side = 'BUY') as buy_orders,
        round(avg(quantity), 2) as avg_quantity,
        round(sum(quantity), 2) as total_quantity,
        uniq(symbol) as unique_symbols
      FROM ${CLICKHOUSE_CONFIG.table}
      WHERE event_time >= now() - INTERVAL 1 HOUR
    `;

    const result = await queryClickHouse(sql);
    return result.data[0] || {};
  } catch (error) {
    console.error('获取统计数据失败:', error);
    return {};
  }
}

// 获取热门交易对
async function getTopSymbols() {
  try {
    const sql = `
      SELECT
        symbol,
        count() as order_count,
        round(sum(quantity), 2) as total_quantity,
        round(avg(price), 2) as avg_price
      FROM ${CLICKHOUSE_CONFIG.table}
      WHERE event_time >= now() - INTERVAL 1 HOUR
      GROUP BY symbol
      ORDER BY order_count DESC
      LIMIT 10
    `;

    const result = await queryClickHouse(sql);
    return result.data || [];
  } catch (error) {
    console.error('获取热门交易对失败:', error);
    return [];
  }
}

// 获取最新强制平仓记录
async function getLatestLiquidations() {
  try {
    const sql = `
      SELECT
        symbol,
        side,
        quantity,
        price,
        event_time as timestamp
      FROM ${CLICKHOUSE_CONFIG.table}
      ORDER BY event_time DESC
      LIMIT 20
    `;

    const result = await queryClickHouse(sql);
    return result.data || [];
  } catch (error) {
    console.error('获取最新记录失败:', error);
    return [];
  }
}

// Redis Stream处理
async function processDataStream() {
  try {
    // 创建Stream（如果不存在）
    await redis.xadd('liquidation:stream', '*', 'type', 'init', 'timestamp', Date.now());
    
    // 创建消费者组
    try {
      await redis.xgroup('CREATE', 'liquidation:stream', 'demo-group', '$', 'MKSTREAM');
    } catch (error) {
      if (!error.message.includes('BUSYGROUP')) {
        console.error('创建消费者组失败:', error);
      }
    }
    
    console.log('✅ Redis Stream初始化完成');
  } catch (error) {
    console.error('❌ Redis Stream初始化失败:', error);
  }
}

// 模拟实时强制平仓数据生产者
async function startLiquidationDataProducer() {
  const symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOGEUSDT', 'XRPUSDT', 'AVAXUSDT', 'DOTUSDT', 'MATICUSDT', 'LINKUSDT'];
  const sides = ['BUY', 'SELL'];

  setInterval(async () => {
    try {
      // 生成1-3个随机强制平仓事件
      const eventCount = Math.floor(Math.random() * 3) + 1;

      for (let i = 0; i < eventCount; i++) {
        const liquidationEvent = {
          symbol: symbols[Math.floor(Math.random() * symbols.length)],
          side: sides[Math.floor(Math.random() * sides.length)],
          quantity: parseFloat((Math.random() * 100 + 1).toFixed(4)),
          price: parseFloat((Math.random() * 50000 + 1000).toFixed(2)),
          timestamp: new Date().toISOString(),
          event_time: Date.now()
        };

        // 推送到Redis Stream
        await redis.xadd('liquidation:realtime', '*',
          'event', JSON.stringify(liquidationEvent),
          'timestamp', Date.now()
        );

        console.log(`🔥 新强制平仓事件: ${liquidationEvent.symbol} ${liquidationEvent.side} ${liquidationEvent.quantity}`);
      }

    } catch (error) {
      console.error('生成强制平仓数据失败:', error);
    }
  }, Math.random() * 2000 + 500); // 随机间隔500ms-2.5s
}

// Redis Stream消费者 - 实时处理强制平仓事件
async function startStreamConsumer() {
  try {
    // 创建消费者组
    try {
      await redis.xgroup('CREATE', 'liquidation:realtime', 'liquidation-processors', '$', 'MKSTREAM');
    } catch (error) {
      if (!error.message.includes('BUSYGROUP')) {
        console.error('创建消费者组失败:', error);
      }
    }

    // 持续消费Stream数据
    const consumeStream = async () => {
      try {
        const results = await redis.xreadgroup(
          'GROUP', 'liquidation-processors', 'consumer-1',
          'COUNT', 10,
          'BLOCK', 1000,
          'STREAMS', 'liquidation:realtime', '>'
        );

        if (results && results.length > 0) {
          const [streamName, messages] = results[0];

          for (const [messageId, fields] of messages) {
            const eventData = JSON.parse(fields[1]); // fields[1] 是 'event' 的值

            // 实时推送给WebSocket客户端
            io.emit('liquidation-event', eventData);

            // 确认消息处理完成
            await redis.xack('liquidation:realtime', 'liquidation-processors', messageId);

            console.log(`✅ 处理强制平仓事件: ${eventData.symbol} ${eventData.side}`);
          }
        }

        // 继续消费
        setImmediate(consumeStream);

      } catch (error) {
        console.error('Stream消费失败:', error);
        // 出错后等待一下再重试
        setTimeout(consumeStream, 1000);
      }
    };

    consumeStream();
    console.log('🚀 Redis Stream消费者启动成功');

  } catch (error) {
    console.error('启动Stream消费者失败:', error);
  }
}

// 定期更新统计数据和热门交易对
async function startPeriodicDataUpdate() {
  setInterval(async () => {
    try {
      const stats = await getRealtimeStats();
      const topSymbols = await getTopSymbols();

      const data = {
        type: 'stats-update',
        timestamp: Date.now(),
        stats,
        topSymbols
      };

      // 推送统计数据更新
      io.emit('stats-update', data);

      console.log(`📊 统计数据更新完成 - ${new Date().toLocaleTimeString()}`);

    } catch (error) {
      console.error('统计数据更新失败:', error);
    }
  }, 10000); // 每10秒更新一次统计数据
}

// API路由
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      redis: 'connected',
      clickhouse: 'connected',
      websocket: 'running'
    }
  });
});

app.get('/api/stats', async (req, res) => {
  try {
    const stats = await getRealtimeStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/symbols', async (req, res) => {
  try {
    const symbols = await getTopSymbols();
    res.json(symbols);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/liquidations', async (req, res) => {
  try {
    const liquidations = await getLatestLiquidations();
    res.json(liquidations);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// WebSocket连接处理
io.on('connection', (socket) => {
  console.log('🔌 客户端连接:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('❌ 客户端断开:', socket.id);
  });
  
  // 发送初始数据
  socket.emit('connected', { message: 'WebSocket连接成功' });
});

// 启动服务器
const PORT = process.env.PORT || 3002;

server.listen(PORT, async () => {
  console.log(`🚀 Redis实时流处理服务器启动在端口 ${PORT}`);
  console.log(`📱 前端页面: http://localhost:${PORT}`);
  console.log(`🔗 API健康检查: http://localhost:${PORT}/api/health`);

  // 初始化Redis Stream
  await processDataStream();

  // 启动实时数据生产者
  startLiquidationDataProducer();

  // 启动Stream消费者
  startStreamConsumer();

  // 启动定期统计数据更新
  startPeriodicDataUpdate();

  console.log('🔥 实时强制平仓流处理系统已启动');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  redis.disconnect();
  redisPub.disconnect();
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
